import { useQuery } from "@tanstack/react-query";

export interface Activity {
  title: string;
  description: string;
  "senpai-count": number;
  hot: boolean;
  fav: boolean;
  tags: string[];
  cover: string;
  icon: string;
}

// Mock function to simulate API call for activities
const fetchActivities = async (): Promise<Activity[]> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 100));

  // Import the activities data
  const activitiesData = await import("../public/mocks/activities.json");
  return activitiesData.default as Activity[];
};

// Enhanced fetch function with filtering and pagination
interface FetchActivitiesOptions {
  searchValue?: string;
  showFavorites?: boolean;
  categoryFilters?: Record<string, boolean | "indeterminate">;
  page?: number;
  itemsPerPage?: number;
}

interface FetchActivitiesResult {
  activities: Activity[];
  totalItems: number;
  totalPages: number;
}

const fetchActivitiesFiltered = async (
  options: FetchActivitiesOptions = {},
): Promise<FetchActivitiesResult> => {
  const {
    searchValue = "",
    showFavorites = false,
    categoryFilters = {},
    page = 1,
    itemsPerPage = 15,
  } = options;

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Import the activities data
  const activitiesData = await import("../public/mocks/activities.json");
  const allActivities = activitiesData.default as Activity[];

  // Filter activities based on the provided options
  const filteredActivities = allActivities.filter((activity) => {
    // Search filter
    if (
      searchValue &&
      !activity.title.toLowerCase().includes(searchValue.toLowerCase())
    ) {
      return false;
    }

    // Favorites filter
    if (showFavorites && !activity.fav) {
      return false;
    }

    // Category filters (placeholder - would need actual category mapping)
    const hasActiveFilters = Object.values(categoryFilters).some(Boolean);
    if (hasActiveFilters) {
      // This would need to be implemented based on how activities are categorized
      // For now, we'll just return true
      return true;
    }

    return true;
  });

  // Pagination
  const totalItems = filteredActivities.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedActivities = filteredActivities.slice(startIndex, endIndex);

  return {
    activities: paginatedActivities,
    totalItems,
    totalPages,
  };
};

export const useActivities = () => {
  return useQuery({
    queryKey: ["activities"],
    queryFn: fetchActivities,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for filtered activities with search, favorites, and pagination
export interface UseActivitiesFiltersOptions {
  searchValue?: string;
  showFavorites?: boolean;
  categoryFilters?: Record<string, boolean | "indeterminate">;
  page?: number;
  itemsPerPage?: number;
}

export const useActivitiesFiltered = (
  options: UseActivitiesFiltersOptions = {},
) => {
  const { page = 1 } = options;

  const queryResult = useQuery({
    queryKey: ["activities", "filtered", options],
    queryFn: () => fetchActivitiesFiltered(options),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data } = queryResult;

  return {
    ...queryResult,
    data: data?.activities,
    totalItems: data?.totalItems || 0,
    totalPages: data?.totalPages || 0,
    currentPage: page,
    hasNextPage: page < (data?.totalPages || 0),
    hasPreviousPage: page > 1,
  };
};

// Enhanced function to get page count with filtering support
interface FetchActivityPageCountOptions {
  searchValue?: string;
  showFavorites?: boolean;
  categoryFilters?: Record<string, boolean | "indeterminate">;
  itemsPerPage?: number;
}

const fetchActivityPageCount = async (
  options: FetchActivityPageCountOptions = {},
): Promise<number> => {
  const {
    searchValue = "",
    showFavorites = false,
    categoryFilters = {},
    itemsPerPage = 15,
  } = options;

  // Import the activities data
  const activitiesData = await import("../public/mocks/activities.json");
  const allActivities = activitiesData.default as Activity[];

  // Apply the same filtering logic as fetchActivitiesFiltered
  const filteredActivities = allActivities.filter((activity) => {
    // Search filter
    if (
      searchValue &&
      !activity.title.toLowerCase().includes(searchValue.toLowerCase())
    ) {
      return false;
    }

    // Favorites filter
    if (showFavorites && !activity.fav) {
      return false;
    }

    // Category filters (placeholder - would need actual category mapping)
    const hasActiveFilters = Object.values(categoryFilters).some(Boolean);
    if (hasActiveFilters) {
      // This would need to be implemented based on how activities are categorized
      // For now, we'll just return true
      return true;
    }

    return true;
  });

  const totalItems = filteredActivities.length;
  return Math.ceil(totalItems / itemsPerPage);
};

// Hook for getting page count with filtering support
export interface UseActivityPageCountOptions {
  searchValue?: string;
  showFavorites?: boolean;
  categoryFilters?: Record<string, boolean | "indeterminate">;
  itemsPerPage?: number;
}

export const useActivityPageCount = (
  options: UseActivityPageCountOptions = {},
) => {
  return useQuery({
    queryKey: ["activityPageCount", options],
    queryFn: () => fetchActivityPageCount(options),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
